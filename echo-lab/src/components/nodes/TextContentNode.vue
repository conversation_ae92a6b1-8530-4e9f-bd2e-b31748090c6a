<!--
  文本内容节点组件
  合并了文本节点和分句节点的功能
-->
<template>
  <BaseNode :nodeId="nodeId">
    <div class="text-content-node">
      <div class="text-preview">
        <div class="preview-header">
          <span>文本内容</span>
          <el-button type="primary" size="small" @click="showTextEditor">
            <el-icon><i-ep-edit /></el-icon>
            编辑文本
          </el-button>
        </div>
        <div class="preview-content" v-if="params.text">
          <pre class="text-display">{{ params.text }}</pre>
        </div>
        <div class="preview-empty" v-else>
          点击"编辑文本"按钮开始输入内容
        </div>
        <div class="text-stats" v-if="params.text">
          <span>{{ getLineCount() }} 行 | {{ params.text.length }} 字符</span>
        </div>
      </div>

      <!-- 分句设置 -->
      <div class="sentence-settings">
        <div class="settings-row">
          <div class="section-title">分句设置：</div>
          <el-select v-model="params.mode" placeholder="分句方式" @change="handleModeChange" style="width: 8.75rem;">
            <el-option label="按段落分句" value="paragraph" />
            <el-option label="按句号分句" value="period" />
            <el-option label="不分句" value="none" />
          </el-select>
        </div>

        <!-- 分隔符部分 (仅在段落模式下显示) -->
        <div v-if="params.mode === 'paragraph'" class="settings-row">
          <div class="section-title">分隔符：</div>
          <el-input v-model="params.splitBy" placeholder="默认为换行符" @change="handleSplitByChange"
            style="width: 8.75rem;" />
        </div>

        <!-- 说话人解析设置 -->
        <div class="settings-row">
          <div class="section-title">说话人解析：</div>
          <div class="speaker-switch">
            <el-switch v-model="params.enableSpeakerParsing" @change="handleSpeakerParsingChange" />
            <el-tooltip content="解析文本中的说话人标记，如'A:','B:'等" placement="top">
              <el-button link icon="QuestionFilled" circle />
            </el-tooltip>
            <el-button type="primary" size="small" @click="openSpeakerMappingDialog"
              :disabled="!params.enableSpeakerParsing">
              <el-icon class="el-icon--left">
                <i-ep-setting />
              </el-icon>
              映射设置
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分句结果预览 -->
      <div class="preview-section"
        v-if="processedResult && processedResult.segments && processedResult.segments.length > 0">
        <div class="preview-header">
          <div>分句结果：</div>
          <el-button type="primary" size="small" @click="showSegmentsDialog">
            <el-icon class="el-icon--left">
              <i-ep-edit-pen />
            </el-icon>
            编辑分句
          </el-button>
        </div>



        <div class="preview-info success-bg">
          已生成 {{ processedResult.segments.length }} 个分句
        </div>
      </div>
      <div v-else class="preview-section">
        <div class="preview-header">
          <div>分句结果：</div>
          <el-button type="primary" size="small" disabled>
            <el-icon class="el-icon--left">
              <i-ep-edit-pen />
            </el-icon>
            编辑分句
          </el-button>
        </div>

        <div class="preview-empty">
          等待文本输入或处理...
        </div>
      </div>

      <!-- 分句编辑弹框 -->
      <standard-dialog v-model="segmentsDialogVisible" title="编辑分句" width="80%" :show-confirm="true" confirm-text="确认"
        @confirm="saveSegments" @cancel="segmentsDialogVisible = false">
        <div class="segments-editor">
          <div class="segments-toolbar">
            <el-tooltip content="添加新分句" placement="top">
              <el-button type="primary" size="small" @click="addSegment">
                <el-icon>
                  <i-ep-plus />
                </el-icon>
                添加分句
              </el-button>
            </el-tooltip>
            <el-tooltip content="重置为原始分句" placement="top">
              <el-button type="warning" size="small" @click="resetSegments">
                <el-icon>
                  <i-ep-refresh-right />
                </el-icon>
                重置分句
              </el-button>
            </el-tooltip>
          </div>

          <div class="segments-table-container">
            <el-table :data="editableSegments" border style="width: 100%">
              <el-table-column label="序号" width="60" align="center">
                <template #default="{ $index }">
                  {{ $index + 1 }}
                </template>
              </el-table-column>

              <el-table-column label="内容">
                <template #default="{ row, $index }">
                  <el-input v-model="row.content" type="textarea" :autosize="{ minRows: 2, maxRows: 8 }"
                    @change="updateSegment($index)" placeholder="请输入分句内容，无长度限制" />
                </template>
              </el-table-column>

              <el-table-column label="语言" width="120">
                <template #default="{ row, $index }">
                  <el-select v-model="row.language" size="small" style="width: 100%;"
                    @change="updateSegmentLanguage($index, row.language)">
                    <el-option v-for="lang in getSupportedLanguages()" :key="lang.value" :label="lang.label"
                      :value="lang.value" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="文本类型" width="120">
                <template #default="{ row, $index }">
                  <el-select v-model="row.type" size="small" style="width: 100%;"
                    @change="updateSegmentType($index, row.type)">
                    <el-option label="普通文本" value="normal" />
                    <el-option label="转场文本" value="transition" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="难度" width="100">
                <template #default="{ row, $index }">
                  <el-select v-model="row.difficulty" size="small" style="width: 100%;"
                    @change="updateSegmentDifficulty($index, row.difficulty)">
                    <el-option label="简单" value="simple" />
                    <el-option label="普通" value="normal" />
                    <el-option label="困难" value="hard" />
                  </el-select>
                </template>
              </el-table-column>

              <!-- 说话人列 -->
              <el-table-column label="说话人" width="100">
                <template #default="{ row }">
                  {{ row.speaker || '默认' }}
                </template>
              </el-table-column>

              <!-- 关键词列 -->
              <el-table-column label="关键词" width="100">
                <template #default="{ row, $index }">
                  <div class="keywords-container">
                    <span class="keyword-count">{{ row.keywords?.length || 0 }}</span>
                    <el-button size="small" type="primary" @click="showKeywordsDialog($index)" class="keyword-btn">
                      管理
                    </el-button>
                  </div>
                </template>
              </el-table-column>


              <el-table-column label="操作" width="140" align="center">
                <template #default="{ $index }">
                  <div class="segment-actions-container">
                    <!-- 操作按钮组 -->
                    <div class="action-buttons">
                      <!-- 上移按钮 -->
                      <el-tooltip content="上移" placement="top" v-if="$index > 0">
                        <el-button size="small" type="primary" @click="moveSegment($index, $index - 1)"
                          class="action-btn">
                          <el-icon>
                            <i-ep-arrow-up />
                          </el-icon>
                        </el-button>
                      </el-tooltip>

                      <!-- 下移按钮 -->
                      <el-tooltip content="下移" placement="top" v-if="$index < editableSegments.length - 1">
                        <el-button size="small" type="primary" @click="moveSegment($index, $index + 1)"
                          class="action-btn">
                          <el-icon>
                            <i-ep-arrow-down />
                          </el-icon>
                        </el-button>
                      </el-tooltip>

                      <!-- 合并上一句按钮 -->
                      <el-tooltip content="合并上一句" placement="top" v-if="$index > 0">
                        <el-button size="small" type="success" @click="mergeWithPrevious($index)" class="action-btn">
                          <el-icon>
                            <i-ep-top />
                          </el-icon>
                        </el-button>
                      </el-tooltip>

                      <!-- 分割按钮 -->
                      <el-tooltip content="分割句子" placement="top">
                        <el-button size="small" type="warning" @click="showSplitDialog($index)" class="action-btn">
                          <el-icon>
                            <i-ep-edit-pen />
                          </el-icon>
                        </el-button>
                      </el-tooltip>

                      <!-- 删除按钮 -->
                      <el-tooltip content="删除句子" placement="top">
                        <el-button size="small" type="danger" @click="removeSegment($index)" class="action-btn">
                          <el-icon>
                            <i-ep-delete />
                          </el-icon>
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </standard-dialog>

      <!-- 分割对话框 -->
      <standard-dialog v-model="splitDialogVisible" title="分割句子" width="50%" :show-confirm="true" confirm-text="确认分割"
        @confirm="confirmSplit" @cancel="splitDialogVisible = false">
        <div class="split-dialog-content">
          <div class="split-header">
            <span class="split-instruction">请使用换行来分割句子</span>
            <el-tooltip content="每行文本将成为一个独立的句子" placement="top">
              <el-button link circle>
                <el-icon>
                  <i-ep-question-filled />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
          <el-input v-model="splitText" type="textarea" :autosize="{ minRows: 8, maxRows: 20 }"
            placeholder="请输入要分割的文本，使用换行分隔" />
        </div>
      </standard-dialog>

      <!-- 说话人映射对话框 -->
      <standard-speaker-mapping-dialog v-model="speakerMappingDialogVisible" @saved="handleSpeakerMappingSaved" />

      <!-- 文本编辑弹框 -->
      <standard-dialog v-model="textEditorVisible" title="编辑文本内容" width="80%" :show-confirm="true" confirm-text="保存"
        :confirm-closes-dialog="false" @confirm="handleSaveClick" @cancel="cancelTextEdit">
        <div class="text-editor-dialog">
          <el-input ref="textEditorInput" type="textarea" v-model="editingText" :autosize="{ minRows: 15, maxRows: 30 }"
            placeholder="请输入文本内容..." maxlength="2000" show-word-limit />
          <div class="editor-stats">
            <span>{{ getEditingLineCount() }} 行 | {{ editingText.length }} 字符</span>
          </div>
        </div>
      </standard-dialog>

      <!-- 关键词管理对话框 -->
      <standard-dialog v-model="keywordsDialogVisible" title="关键词管理" width="50%" :show-confirm="true" confirm-text="确认"
        @confirm="confirmKeywords" @cancel="keywordsDialogVisible = false">
        <div class="keywords-dialog-content">
          <div class="keywords-header">
            <span class="keywords-instruction">为句子添加关键词</span>
            <el-tooltip content="关键词将在播放时被特殊处理" placement="top">
              <el-button link circle>
                <el-icon>
                  <i-ep-question-filled />
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>

          <!-- 当前句子内容 -->
          <div class="current-segment">
            <div class="segment-label">当前句子：</div>
            <div class="segment-content">{{ currentSegment?.content || '' }}</div>
          </div>

          <!-- 关键词列表 -->
          <div class="keywords-list">
            <div class="keywords-label">关键词列表：</div>
            <div v-if="!editableKeywords.length" class="no-keywords">
              暂无关键词，请点击下方按钮添加
            </div>
            <div v-else class="keywords-items">
              <div v-for="(keyword, index) in editableKeywords" :key="index" class="keyword-item">
                <el-input v-model="keyword.text" placeholder="请输入关键词" class="keyword-input" />
                <el-button type="danger" size="small" @click="removeKeyword(index)" class="keyword-remove-btn">
                  <el-icon>
                    <i-ep-delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <!-- 添加关键词按钮 -->
          <div class="add-keyword-container">
            <el-button type="primary" @click="addKeyword" class="add-keyword-btn">
              <el-icon>
                <i-ep-plus />
              </el-icon>
              添加关键词
            </el-button>
          </div>
        </div>
      </standard-dialog>
    </div>
  </BaseNode>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import BaseNode from './BaseNode.vue';
import StandardDialog from '../common/StandardDialog.vue';
import StandardSpeakerMappingDialog from '../settings/StandardSpeakerMappingDialog.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { md5 } from '@/utils/md5';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import { detectLanguage } from '@/utils/languageDetection';
import { parseSpeaker } from '@/core/processors/textContentNodeProcessor';
import { generateUniqueSegmentId } from '@/utils/segmentIdGenerator';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['node-updated']);

const nodeStore = useNodeStore();

// 节点数据
const node = computed(() => nodeStore.getNode(props.nodeId));

// 节点参数
const params = computed({
  get: () => node.value?.params || {
    text: '',
    mode: 'paragraph',
    splitBy: '\n',
    segments: [],
    enableSpeakerParsing: true
  },
  set: (value) => nodeStore.updateNodeParams(props.nodeId, value)
});

// 处理结果
const processedResult = ref(null);

// 分句编辑相关
const segmentsDialogVisible = ref(false);
const editableSegments = ref([]);

// 分割对话框相关
const splitDialogVisible = ref(false);
const splitText = ref('');
const currentSplitIndex = ref(-1);

// 说话人映射对话框
const speakerMappingDialogVisible = ref(false);

// 文本编辑相关
const textEditorVisible = ref(false);
const editingText = ref('');

// 关键词管理相关
const keywordsDialogVisible = ref(false);
const currentKeywordIndex = ref(-1);
const currentSegment = ref(null);
const editableKeywords = ref([]);



// 统一的参数变化处理函数
function handleParamChange() {
  // 更新节点参数
  nodeStore.updateNodeParams(props.nodeId, params.value);
  // 处理节点
  processNode();
}

// 处理文本变化 - 已移除，直接在doSaveTextContent中处理

// 处理模式变化
const handleModeChange = handleParamChange;

// 处理分隔符变化
const handleSplitByChange = handleParamChange;

// 处理说话人解析开关变化
const handleSpeakerParsingChange = handleParamChange;

// 获取行数
function getLineCount() {
  if (!params.value.text) return 0;
  return params.value.text.split('\n').length;
}

// 获取编辑中文本的行数
function getEditingLineCount() {
  if (!editingText.value) return 0;
  return editingText.value.split('\n').length;
}

// 显示文本编辑器
function showTextEditor() {
  editingText.value = params.value.text || '';
  textEditorVisible.value = true;
}

// 处理保存按钮点击
function handleSaveClick() {
  const oldSegments = params.value.segments || [];

  // 检查是否有自定义设置
  const hasCustomSettings = oldSegments.some(seg =>
    (seg.keywords && seg.keywords.length > 0) ||
    seg.type !== 'normal'
  );

  if (hasCustomSettings) {
    ElMessageBox.confirm(
      '保存文本将重新生成分句，可能导致已设置的关键词、文本类型等信息丢失。是否继续保存？',
      '确认保存',
      {
        confirmButtonText: '确认保存',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 用户确认后才执行保存并关闭弹框
      doSaveTextContent();
    }).catch(() => {
      // 用户取消，保持编辑弹框打开
    });
  } else {
    // 没有自定义设置，直接保存
    doSaveTextContent();
  }
}

// 执行保存文本内容的具体逻辑
function doSaveTextContent() {
  // 设置保存标志位，避免watch重复处理
  isSavingText.value = true;

  try {
    // 更新文本内容
    params.value.text = editingText.value;

    // 关闭编辑弹框
    textEditorVisible.value = false;

    // 智能保留关键词数据
    preserveKeywordsOnTextChange();

    // 更新节点参数并重新处理节点
    nodeStore.updateNodeParams(props.nodeId, params.value);
    processNode();
  } finally {
    // 重置保存标志位
    isSavingText.value = false;
  }
}

// 取消文本编辑
function cancelTextEdit() {
  textEditorVisible.value = false;
  editingText.value = '';
}

// 智能保留关键词数据
function preserveKeywordsOnTextChange() {
  const oldSegments = params.value.segments || [];

  // 如果没有旧分句或没有关键词数据，直接清空
  if (oldSegments.length === 0 || !oldSegments.some(seg => seg.keywords && seg.keywords.length > 0)) {
    params.value.segments = [];
    return;
  }

  // 创建完整内容到分句数据的映射表（包含说话人信息）
  const contentToSegmentDataMap = new Map();
  oldSegments.forEach(segment => {
    if (segment.keywords && segment.keywords.length > 0) {
      // 使用完整的分句内容作为key，包括说话人标记
      const fullContent = segment.content;
      // 如果已存在相同内容，说明有重复分句，需要区分处理
      if (contentToSegmentDataMap.has(fullContent)) {
        // 对于重复内容，我们不进行自动恢复，避免混淆
        console.warn('发现重复分句内容，跳过关键词恢复:', fullContent);
        return;
      }

      contentToSegmentDataMap.set(fullContent, {
        keywords: segment.keywords,
        type: segment.type,
        difficulty: segment.difficulty,
        speaker: segment.speaker,
        originalId: segment.id // 保存原始ID用于调试
      });
    }
  });

  // 临时保存映射表，在重新生成分句后使用
  params.value._tempKeywordsMap = contentToSegmentDataMap;

  // 清空segments让系统重新生成分句
  params.value.segments = [];
}

// 从映射表恢复关键词数据
function restoreKeywordsFromMap(newSegments, keywordsMap) {
  if (!newSegments || !keywordsMap || keywordsMap.size === 0) {
    return false;
  }

  let restoredCount = 0;

  newSegments.forEach(segment => {
    // 使用完整的分句内容进行精确匹配
    const fullContent = segment.content;
    const savedData = keywordsMap.get(fullContent);

    if (savedData) {
      // 恢复关键词数据
      segment.keywords = savedData.keywords || [];

      // 恢复其他自定义设置
      if (savedData.type && savedData.type !== 'normal') {
        segment.type = savedData.type;
      }
      if (savedData.difficulty && savedData.difficulty !== 'normal') {
        segment.difficulty = savedData.difficulty;
      }
      if (savedData.speaker) {
        segment.speaker = savedData.speaker;
      }

      console.log(`恢复分句关键词: "${fullContent}" -> ${savedData.keywords.length}个关键词`);
      restoredCount++;
    }
  });

  // 只更新segments数据，不触发nodeStore更新避免循环
  params.value.segments = newSegments;

  if (restoredCount > 0) {
    ElMessage.success(`已恢复 ${restoredCount} 个分句的关键词和自定义设置`);
    return true;
  } else {
    console.log('没有找到可恢复的关键词数据');
    return false;
  }
}



// 处理节点
function processNode() {
  return new Promise((resolve) => {
    try {
      processedResult.value = nodeStore.processNode(props.nodeId);

      // 如果有临时保存的关键词映射，尝试恢复关键词数据
      if (params.value._tempKeywordsMap && processedResult.value && processedResult.value.segments) {
        const restored = restoreKeywordsFromMap(processedResult.value.segments, params.value._tempKeywordsMap);
        // 清理临时映射表
        delete params.value._tempKeywordsMap;

        // 如果恢复了关键词数据，需要更新nodeStore并重新处理
        if (restored) {
          nodeStore.updateNodeParams(props.nodeId, params.value);
          // 重新处理节点以确保数据一致性
          processedResult.value = nodeStore.processNode(props.nodeId);
        }
      }

      // 通知父组件节点已更新
      emit('node-updated', props.nodeId);

      resolve(processedResult.value);
    } catch (error) {
      console.error('处理节点失败:', error);
      processedResult.value = null;
      resolve(null);
    }
  });
}

// 组件挂载时初始化
onMounted(() => {
  // 如果节点不存在，创建新节点
  if (!node.value) {
    return;
  }

  // 处理节点
  processNode();
});

// 监听文本内容变化（避免深度监听导致的性能问题）
// 添加标志位避免在保存过程中重复处理
const isSavingText = ref(false);

watch(() => params.value.text, () => {
  // 如果正在保存文本，跳过自动处理
  if (isSavingText.value) {
    return;
  }
  processNode();
});

// 显示分句编辑对话框
function showSegmentsDialog() {
  // 使用节点参数中的segments或处理结果中的segments
  if (params.value.segments && params.value.segments.length > 0) {
    editableSegments.value = JSON.parse(JSON.stringify(params.value.segments));
  } else if (processedResult.value && processedResult.value.segments) {
    editableSegments.value = JSON.parse(JSON.stringify(processedResult.value.segments));
  } else {
    editableSegments.value = [];
  }

  // 显示对话框
  segmentsDialogVisible.value = true;
}

// 添加新分句
function addSegment() {
  // 获取现有ID列表
  const existingIds = editableSegments.value.map(seg => seg.id).filter(id => id);

  // 为空内容生成唯一ID
  const content = '';
  const segmentId = generateUniqueSegmentId(content, existingIds);

  // 添加新分句
  editableSegments.value.push({
    id: segmentId,
    content: content,
    language: 'auto', // 初始设置为auto，等待用户输入内容后再检测
    type: 'normal', // 默认为普通文本类型
    difficulty: 'normal', // 默认为普通难度
    speaker: null, // 默认为null，让系统自动处理
    keywords: [] // 添加关键词数组字段
  });
}

// 移除分句
function removeSegment(index) {
  if (index >= 0 && index < editableSegments.value.length) {
    editableSegments.value.splice(index, 1);
    ElMessage.success(`已删除第${index + 1}句`);
  }
}

// 更新分句内容
function updateSegment(index) {
  // 获取当前分句
  const segment = editableSegments.value[index];
  if (segment && segment.content) {
    // 获取所有现有ID（除了当前分句）
    const existingIds = editableSegments.value
      .filter((_, i) => i !== index)
      .map(seg => seg.id)
      .filter(id => id); // 过滤掉空ID

    // 生成唯一ID
    segment.id = generateUniqueSegmentId(segment.content, existingIds);

    // 如果语言是auto，自动检测语言
    if (!segment.language || segment.language === 'auto') {
      segment.language = detectLanguage(segment.content);
    }
  }
}

// 移动分句位置
function moveSegment(fromIndex, toIndex) {
  if (
    fromIndex >= 0 &&
    fromIndex < editableSegments.value.length &&
    toIndex >= 0 &&
    toIndex < editableSegments.value.length
  ) {
    // 获取要移动的分句
    const segment = editableSegments.value[fromIndex];

    // 从原位置删除
    editableSegments.value.splice(fromIndex, 1);

    // 插入到新位置
    editableSegments.value.splice(toIndex, 0, segment);

    ElMessage.success(`已将第${fromIndex + 1}句移动到第${toIndex + 1}句位置`);
  }
}

// 合并上一句
function mergeWithPrevious(index) {
  if (index > 0 && index < editableSegments.value.length) {
    // 获取当前句和上一句
    const currentSegment = editableSegments.value[index];
    const previousSegment = editableSegments.value[index - 1];

    // 如果两个句子的说话人不同，提示用户
    if (currentSegment.speaker && previousSegment.speaker &&
      currentSegment.speaker !== previousSegment.speaker) {
      ElMessageBox.confirm(
        `当前句的说话人(${currentSegment.speaker})与上一句的说话人(${previousSegment.speaker})不同，合并后将使用上一句的说话人。是否继续？`,
        '说话人不同',
        {
          confirmButtonText: '继续合并',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 用户确认后继续合并
        doCombine(previousSegment, currentSegment, index);
      }).catch(() => {
        // 用户取消，不做操作
      });
    } else {
      // 说话人相同或没有说话人，直接合并
      doCombine(previousSegment, currentSegment, index);
    }
  }
}

// 执行合并操作
function doCombine(previousSegment, currentSegment, index) {
  // 合并内容
  previousSegment.content = previousSegment.content + currentSegment.content;

  // 获取现有ID列表（排除当前要合并的两个分句）
  const existingIds = editableSegments.value
    .filter((_, i) => i !== index - 1 && i !== index)
    .map(seg => seg.id)
    .filter(id => id);

  // 生成唯一ID
  previousSegment.id = generateUniqueSegmentId(previousSegment.content, existingIds);

  // 合并后移除原有的关键词，用户需要重新为合并后的句子添加关键词
  previousSegment.keywords = [];

  // 移除当前句
  editableSegments.value.splice(index, 1);

  // 如果语言是auto，自动检测语言
  if (!previousSegment.language || previousSegment.language === 'auto') {
    previousSegment.language = detectLanguage(previousSegment.content);
  }

  ElMessage.success(`已合并第${index}句和第${index + 1}句`);
}

// 重置分句
async function resetSegments() {
  // 保存旧的分句ID映射，用于保持资源数据的关联
  const oldSegments = params.value.segments || [];
  const contentToIdMap = new Map();

  // 建立内容到ID的映射
  oldSegments.forEach(segment => {
    if (segment.content) {
      // 使用去除说话人标记后的内容作为key
      const { content } = parseSpeaker(segment.content);
      contentToIdMap.set(content, segment.id);
    }
  });

  // 清空segments，让处理器重新生成
  params.value.segments = [];

  // 更新节点参数并重新处理
  await nodeStore.updateNodeParams(props.nodeId, params.value);
  await processNode();

  // 处理完成后，尝试恢复旧的ID
  if (processedResult.value && processedResult.value.segments) {
    const newSegments = processedResult.value.segments.map(segment => {
      // 检查是否有对应的旧ID
      const oldId = contentToIdMap.get(segment.content);
      if (oldId) {
        // 使用旧ID，保持资源数据的关联
        return {
          ...segment,
          id: oldId
        };
      }
      return segment;
    });

    // 更新分句数据
    params.value.segments = newSegments;
    await nodeStore.updateNodeParams(props.nodeId, params.value);

    // 重新处理一次，确保数据一致
    await processNode();
  }

  ElMessage.success('已重置分句');
}

// 保存分句
function saveSegments() {
  // 过滤掉空内容的分句
  const validSegments = editableSegments.value.filter(segment => segment.content);

  // 确保所有分句都有唯一ID
  const existingIds = [];
  const updatedSegments = validSegments.map(segment => {
    // 如果已有ID且不重复，保持不变
    if (segment.id && !existingIds.includes(segment.id)) {
      existingIds.push(segment.id);
      return {
        ...segment,
        content: segment.content
      };
    }

    // 生成唯一ID
    const uniqueId = generateUniqueSegmentId(segment.content, existingIds);
    existingIds.push(uniqueId);

    return {
      ...segment,
      id: uniqueId,
      content: segment.content
    };
  });

  // 更新节点参数
  params.value.segments = updatedSegments;

  // 同步到节点参数
  nodeStore.updateNodeParams(props.nodeId, params.value);

  // 重新处理节点，确保结果tab有数据
  processNode();

  // 关闭对话框
  segmentsDialogVisible.value = false;

  ElMessage.success(`已保存 ${updatedSegments.length} 个分句`);
}

// 显示分割对话框
function showSplitDialog(index) {
  // 获取要分割的句子
  const segment = editableSegments.value[index];

  if (!segment) {
    return;
  }

  // 设置分割文本和索引
  splitText.value = segment.content;
  currentSplitIndex.value = index;

  // 显示对话框
  splitDialogVisible.value = true;
}

// 获取支持的语言列表
function getSupportedLanguages() {
  return SUPPORTED_LANGUAGES;
}

// 更新分句语言
function updateSegmentLanguage(index, language) {
  if (index >= 0 && index < editableSegments.value.length) {
    // 更新语言
    editableSegments.value[index].language = language;
  }
}

// 更新分句类型
function updateSegmentType(index, type) {
  if (index >= 0 && index < editableSegments.value.length) {
    // 更新类型
    editableSegments.value[index].type = type;

    // 如果是转场文本，自动将说话人设置为default
    if (type === 'transition') {
      editableSegments.value[index].speaker = 'default';
      ElMessage.success('转场文本已自动设置为默认说话人');
    }
  }
}

// 更新分句难度
function updateSegmentDifficulty(index, difficulty) {
  if (index >= 0 && index < editableSegments.value.length) {
    editableSegments.value[index].difficulty = difficulty;
  }
}

// 显示关键词管理对话框
function showKeywordsDialog(index) {
  if (index >= 0 && index < editableSegments.value.length) {
    // 保存当前索引
    currentKeywordIndex.value = index;

    // 保存当前分句
    currentSegment.value = editableSegments.value[index];

    // 复制关键词列表
    editableKeywords.value = currentSegment.value.keywords ?
      JSON.parse(JSON.stringify(currentSegment.value.keywords)) : [];

    // 显示对话框
    keywordsDialogVisible.value = true;
  }
}

// 添加关键词
function addKeyword() {
  // 使用空字符串的MD5作为临时ID，仅用于Vue列表渲染的key
  // 这个临时ID在用户确认时会被替换为基于内容和句子ID的正式ID
  const keywordId = `kw_${md5('')}`;

  // 添加新关键词到编辑列表
  editableKeywords.value.push({
    id: keywordId, // 临时ID，确认时会重新生成
    text: '' // 空内容，等待用户填写
  });
}

// 移除关键词
function removeKeyword(index) {
  if (index >= 0 && index < editableKeywords.value.length) {
    editableKeywords.value.splice(index, 1);
  }
}

// 确认关键词修改
function confirmKeywords() {
  if (currentKeywordIndex.value < 0 || !currentSegment.value) {
    keywordsDialogVisible.value = false;
    return;
  }

  // 过滤掉空内容的关键词
  const validKeywords = editableKeywords.value.filter(kw => kw.text);

  // 生成正式的关键词ID，包含句子ID以避免重复
  // 格式：kw_{句子ID}_{关键词内容MD5}
  const updatedKeywords = validKeywords.map(kw => ({
    id: `kw_${currentSegment.value.id}_${md5(kw.text)}`,
    text: kw.text
  }));

  // 更新分句的关键词
  if (currentKeywordIndex.value >= 0 && currentKeywordIndex.value < editableSegments.value.length) {
    editableSegments.value[currentKeywordIndex.value].keywords = updatedKeywords;
    console.log('confirmKeywords: 已写入 editableSegments', JSON.stringify(editableSegments.value[currentKeywordIndex.value], null, 2));
  }

  // 关闭对话框
  keywordsDialogVisible.value = false;

  ElMessage.success(`已更新关键词，共${updatedKeywords.length}个`);
}

// 打开说话人映射对话框
function openSpeakerMappingDialog() {
  speakerMappingDialogVisible.value = true;
}

// 处理说话人映射保存
function handleSpeakerMappingSaved(mappings) {
  // 映射已经在对话框组件中保存到本地存储
  console.log("TextContentNode - 收到保存的说话人映射:", mappings);

  ElMessage.success('说话人映射已保存并应用');

  // 重新处理节点，应用新的映射关系
  processNode();
}

// 确认分割
function confirmSplit() {
  if (currentSplitIndex.value < 0 || !splitText.value) {
    splitDialogVisible.value = false;
    return;
  }

  // 使用换行分割文本
  const parts = splitText.value.split('\n').map(p => p.trim()).filter(p => p);

  if (parts.length <= 1) {
    ElMessage.warning('未检测到多行文本，请使用换行来分割句子');
    return;
  }

  // 获取当前段落索引
  const currentIndex = currentSplitIndex.value;

  // 获取当前段落
  const currentSegment = editableSegments.value[currentIndex];
  if (!currentSegment) {
    return;
  }

  // 获取所有现有ID
  const existingIds = editableSegments.value.map(seg => seg.id).filter(id => id);

  // 更新当前段落内容为第一部分
  currentSegment.content = parts[0];

  // 为当前段落生成唯一ID
  currentSegment.id = generateUniqueSegmentId(parts[0], existingIds);
  existingIds.push(currentSegment.id);

  // 分割后移除原句的关键词，用户需要重新为每个句子添加关键词
  currentSegment.keywords = [];

  // 如果语言是auto，自动检测语言
  if (!currentSegment.language || currentSegment.language === 'auto') {
    currentSegment.language = parts[0] ? detectLanguage(parts[0]) : 'auto';
  }

  // 创建新段落
  const newSegments = parts.slice(1).map(content => {
    // 确定类型和说话人
    const type = currentSegment.type || 'normal';
    let speaker = currentSegment.speaker;

    // 如果是转场文本，自动将说话人设置为default
    if (type === 'transition') {
      speaker = 'default';
    }

    // 为新段落生成唯一ID
    const uniqueId = generateUniqueSegmentId(content, existingIds);
    existingIds.push(uniqueId);

    return {
      id: uniqueId,
      content: content,
      language: content ? detectLanguage(content) : 'auto',
      speaker: speaker, // 根据类型设置说话人信息
      type: type, // 继承原句的文本类型，默认为普通文本
      difficulty: 'normal', // 分割后的新句子默认为普通难度
      keywords: [] // 添加关键词数组字段
    };
  });

  // 插入新段落
  editableSegments.value.splice(currentIndex + 1, 0, ...newSegments);

  ElMessage.success(`已将句子分割为 ${parts.length} 个部分`);

  // 关闭对话框
  splitDialogVisible.value = false;
}
</script>

<style scoped>
.text-content-node {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem;
}

/* 删除语言选择器样式 */

/* 文本预览区域样式 */
.text-preview {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
  color: #606266;
}

.preview-content {
  padding: 12px;
  height: 60px;
  overflow: hidden;
}

.text-display {
  margin: 0;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  color: #606266;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.preview-empty {
  padding: 40px 12px;
  text-align: center;
  color: #909399;
  font-style: italic;
  background-color: #fafafa;
}

.text-stats {
  padding: 8px 12px;
  font-size: 0.75rem;
  color: #909399;
  text-align: right;
  background-color: #f9f9f9;
  border-top: 1px solid #ebeef5;
}

/* 文本编辑弹框样式 */
.text-editor-dialog {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.editor-stats {
  font-size: 0.75rem;
  color: #909399;
  text-align: right;
}



/* 操作区样式 */
.segment-actions-container {
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
  max-width: 120px;
}

.action-btn {
  padding: 6px;
  height: 28px;
  width: 28px;
  margin: 0;
  font-size: 12px;
}

.sentence-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #ebeef5;
}

.settings-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.section-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 0.25rem;
}

.split-section {
  margin-top: 0.5rem;
}

.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.preview-empty {
  text-align: center;
  color: #909399;
  font-size: 0.875rem;
  padding: 0.625rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}



.preview-actions {
  margin-bottom: 0.5rem;
}

.empty-preview {
  margin-top: 0.5rem;
}

.segments-editor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.segments-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.segment-edit-item {
  margin-bottom: 0.75rem;
  border: 1px solid #ebeef5;
  border-radius: 0.25rem;
  padding: 0.5rem;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.segment-actions {
  display: flex;
  gap: 0.25rem;
}

.segment-actions-group {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.25rem;
  justify-content: center;
}

.segment-brief {
  margin-top: 0.25rem;
  color: #606266;
  font-size: 0.875rem;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dialog-toolbar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0.5rem;
}



.split-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 0.9375rem;
}

.split-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.split-instruction {
  font-weight: bold;
  color: #606266;
}

/* 图标样式 */
.el-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  line-height: 1;
}

.speaker-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 关键词管理样式 */
.keywords-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

.keyword-count {
  font-size: 0.875rem;
  color: #606266;
  background-color: #f5f7fa;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  min-width: 1.5rem;
  text-align: center;
}

.keyword-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.keywords-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.keywords-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.keywords-instruction {
  font-weight: bold;
  color: #606266;
}

.current-segment {
  background-color: #f5f7fa;
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

.segment-label {
  font-weight: bold;
  margin-bottom: 0.25rem;
  color: #606266;
}

.segment-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.keywords-list {
  margin-top: 0.5rem;
}

.keywords-label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #606266;
}

.no-keywords {
  color: #909399;
  font-style: italic;
  padding: 0.5rem;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

.keywords-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.keyword-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.keyword-input {
  flex: 1;
}

.add-keyword-container {
  margin-top: 0.5rem;
  display: flex;
  justify-content: center;
}
</style>
